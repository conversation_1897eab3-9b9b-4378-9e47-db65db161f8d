@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

:root {
    --qx-primary: #2271b1;
    --qx-primary-light: #3582c4;
    --qx-primary-dark: #135e96;

    --qx-error: #f44336;
    --qx-success: #3cf436;
    --qx-warning: #f49b36;

    --qx-text: #333333;
    --qx-text-primary: #3e5370;
    --qx-text-light: #666666;
    --qx-text-dark: #111111;

    --qx-border: #e5e7eb;
    --qx-border-light: #dcdcde;
    --qx-border-dark: #a7aaad;

   

    --qx-agent-bg: #f8fafc;
    --qx-user-bg: #e3f2fd;

    --qx-shadow-light: 0 4px 8px -2px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --qx-shadow-medium: 0 6px 12px -3px rgba(0, 0, 0, 0.15), 0 3px 6px -2px rgba(0, 0, 0, 0.1);
    --qx-shadow-dark: 0 8px 16px -4px rgba(0, 0, 0, 0.2), 0 4px 8px -3px rgba(0, 0, 0, 0.15);

    --qx-gradient-rigth: linear-gradient(to right, var(--qx-bg), var(--qx-hover));
    --qx-gradient-blue: linear-gradient(135deg, var(--qx-primary) 0%, var(--qx-primary-dark) 100%);
    --qx-gradient-bottom: linear-gradient(to bottom, var(--qx-bg), var(--qx-hover));

    --qx-radius-sm: 4px;
    --qx-radius-md: 8px;
    --qx-radius-lg: 16px;
    --qx-radius-full: 50%;

    --qx-padding:8px 12px;

    --qx-gap-xs: 0.25rem;
    --qx-gap-sm: 0.5rem;
    --qx-gap-md: 0.75rem;
    --qx-gap-lg: 1rem;
    --qx-gap-xl: 1.5rem;

    --qx-button-xs: 0.25rem 0.5rem;
    --qx-button-sm: 0.5rem 1rem;
    --qx-button-md: 0.75rem 1.5rem;
    --qx-button-lg: 1rem 2rem;
    --qx-button-xl: 1.25rem 2.5rem;

    --qx-font-family: 'Inter', sans-serif;

    --qx-font-xs: 0.5rem;
    --qx-font-sm: 0.7rem;
    --qx-font-base: 1rem;
    --qx-font-lg: 1.25rem;
    --qx-font-xl: 1.5rem;
    --qx-font-xxl: 2rem;

}



/* Main Container Styles */
.qx-wrap {
    max-width: 100%;
    padding: 10px 30px;
    font-family: var(--qx-font-family);
}

.qx-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--qx-border-light);
    padding: 10px;
}

/* Header Styles */
.qx-wrap h1 {
    color: var(--qx-text-primary);
    font-size: var(--qx-font-xl);
    font-weight: var(--qx-weight-semibold);
}

.xero-manual-update {
    display: flex;
    gap: 100px;
    justify-content: space-between;
    align-items: center;
    background: #f6f7f7;
    border: 1px solid #dcdcde;
    border-radius: var(--qx-radius-md);
    padding: 20px;
    margin: 20px 0;
}

/* Button Add */
#xero-manual-update-button,
.qx-add-field-mapping,
.xero-add-card,
.submit-button,
.qx-add-scope {
    background: var(--qx-primary);
    border: none;
    border-radius: var(--qx-radius-md);
    color: #fff;
    cursor: pointer;
    padding: var(--qx-button-sm);
    display: inline-flex;
    align-items: center;
    gap: var(--qx-gap-sm);
    transition: all 0.2s ease;
}

/* Button Remove */
.qx-remove-scope,
.qx-remove-card,
.qx-remove-mapping   {
    background: var(--qx-error);
    border: none;
    border-radius: var(--qx-radius-md);
    color: #fff;
    cursor: pointer;
    padding: var(--qx-button-sm);
    display: inline-flex;
    align-items: center;
    gap: var(--qx-gap-sm);
    transition: all 0.2s ease;
    margin: 20px;
}





/* Alert Styles */
.alert {
    border-radius: var(--qx-radius-sm);
    margin: var(--qx-gap-sm) 0;
    padding: 12px 15px;
}

.alert-success {
    background-color: var(--qx-success);
    border: 1px solid var(--qx-success);
    color: #155724;
}

.alert-error {
    background-color: var(--qx-error);
    border: 1px solid var(--qx-error);
    color: #721c24;
}

/* Responsive Adjustments */
@media screen and (max-width: 782px) {
    .form-table th {
        width: 100%;
        display: block;
        padding-bottom: 0;
    }

    .form-table td {
        padding-left: 0;
        padding-right: 0;
    }

    .form-group {
        min-width: 100%;
    }
}

/* Input Styles */
input[type="text"],
input[type="password"],
select,
textarea {
    border: 1px solid var(--qx-border-dark);
    border-radius: var(--qx-radius-md);
    padding: var(--qx-padding);
    width: 100%;
    max-width: 400px;
    transition: all 0.2s ease;
}

input[type="text"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Description Text */
.description {
    color: #6e757e;
    font-size: 13px;
    margin-top: 5px;
    font-style: italic;
}

/* Form Mapping Card Styles */
.form-mapping-card {
    background: #fff;
    border: 1px solid #dcdcde;
    border-radius: 6px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-header {
    background: #f6f7f7;
    border-bottom: 1px solid #dcdcde;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 6px 6px 0 0;
}

.card-header h4 {
    margin: 0;
    color: #1d2327;
    font-size: 16px;
    font-weight: 500;
}

.form-mapping-fields {
    padding: 20px;
}

.form-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f1;
}

.form-section h5 {
    color: #1d2327;
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
    min-width: 250px;
}

.form-group.full-width {
    flex: 0 0 100%;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}




/* Field Mapping Styles */
.field-mapping-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.field-mapping-table {
    width: 100%;
    margin-bottom: 15px;
}

.field-mapping-header {
    display: flex;
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px 4px 0 0;
    font-weight: 600;
}

.field-mapping-row {
    display: flex;
    padding: 10px;
    border-bottom: 1px solid #eee;
    align-items: center;
    transition: background-color 0.2s;
}

.field-mapping-row:hover {
    background-color: #f9f9f9;
}

.field-mapping-column {
    flex: 1;
    padding: 0 10px;
}

.field-mapping-column.actions {
    flex: 0 0 80px;
    text-align: right;
}

.field-mapping-column input,
.field-mapping-column select {
    width: 100%;
    max-width: none;
}




.field-mapping-help {
    margin-top: 10px;
    font-style: italic;
    color: #666;
}

/* Toggle Card Animation */
.form-mapping-fields {
    transition: all 0.3s ease;
}

/* Loading Spinner Animation */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Sync Button Styles */
.xero-sync-button,
.xero-continue-button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 150px;
}

.xero-sync-button .button-content,
.xero-continue-button .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.xero-sync-button.loading,
.xero-continue-button.loading {
    background-color: var(--qx-primary-dark);
}

/* Scope Items Styling */
.scope-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    animation: fadeIn 0.3s ease;
}

.scope-item input {
    flex: 1;
}

.qx-add-scope {
    margin: 10px;
}

/* Tab Content Animation */
.tab-content {
    animation: fadeIn 0.3s ease;
}

/* Custom Switch Styling */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: #2271b1;
}

input:checked+.slider:before {
    transform: translateX(26px);
}

/* Tab Navigation Styles */
.nav-tab-wrapper {
    border-bottom: 2px solid #c3c4c7;
    margin-bottom: 30px;
    padding: 0;
}

.nav-tab {
    background: #f0f0f1;
    border: 1px solid #c3c4c7;
    border-radius: 4px 4px 0 0;
    border-bottom: none;
    color: #50575e;
    font-size: 14px;
    font-weight: 500;
    margin-right: 5px;
    padding: 12px 20px;
    transition: all 0.2s ease;
}

.nav-tab:hover {
    background: #fff;
    color: #135e96;
}

/* Form Styles */
.form-table {
    margin-top: 20px;
}

.form-table th {
    padding: 20px 10px 20px 0;
    width: 200px;
    font-weight: 600;
}

.form-table td {
    padding: 20px 10px;
}

/* Animation Styles */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Error Styles */
.error {
    border-color: #dc3232 !important;
}

/* Import Progress Styles */
.xero-modal-header.import-complete {
    background-color: #1e7e34;
}

.xero-modal-header.import-complete h3 {
    color: #fff;
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    height: 20px;
    background-color: #f1f1f1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar-fill {
    height: 100%;
    background-color: var(--qx-primary);
    width: 0%;
    transition: width 0.5s ease;
}

.progress-bar .progress-bar-fill.complete {
    background-color: #28a745;
    transition: background-color 0.3s ease;
}

.progress-text {
    font-size: 14px;
    color: #666;
}

.import-details {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.modal-actions {
    margin-top: 15px;
    text-align: right;
}

.cancel-import-button {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.cancel-import-button:hover {
    background-color: #d32f2f;
}

.rate-limit-warning {
    background-color: #fff3cd;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    border-left: 4px solid #ffc107;
}

.rate-limited {
    display: inline-block;
    background-color: #f44336;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.rate-normal {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.rate-limit-timer {
    font-weight: bold;
    color: #856404;
    margin-top: 5px;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
}

/* Modal Styles */
.xero-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.xero-modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.xero-modal-header {
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.xero-modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.xero-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.xero-modal-close:hover,
.xero-modal-close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

/* Confirmation Dialog Styles */
.confirmation-message {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.confirm-sync {
    background-color: var(--qx-primary);
    color: white;
}

.confirm-sync:hover {
    background-color: var(--qx-primary-dark);
}

.cancel-sync {
    background-color: #f1f1f1;
    color: #333;
}

.cancel-sync:hover {
    background-color: #e1e1e1;
}

/* Sync Button Styles */



@keyframes xero-spin {
    to {
        transform: rotate(360deg);
    }
}

/* Modal Styles */
.xero-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: modal-bg-appear 0.3s ease-out;
    backdrop-filter: blur(2px);
}

@keyframes modal-bg-appear {
    from {
        background-color: rgba(0, 0, 0, 0);
    }

    to {
        background-color: rgba(0, 0, 0, 0.5);
    }
}

.xero-modal-content {
    background-color: #ffffff;
    margin: 10% auto;
    border: none;
    width: 80%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    contain: content;
    animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.xero-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #F4F5F7;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.xero-modal-header h3 {
    margin: 0;
    color: #333333;
    font-size: 20px;
    font-weight: 500;
}

.xero-modal-body {
    padding: 15px 20px;
}

/* Progress Bar Styles */
.progress-bar {
    background-color: #f0f0f0;
    border-radius: 4px;
    height: 20px;
    overflow: hidden;
    position: relative;
}

.progress-bar-fill {
    background-color: #2271b1;
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar-pulse {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* Button Styles */
.xero-modal button.button {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* Cancel button styles */
.xero-cancel-import,
.xero-cancel-confirm {
    background-color: #f1f1f1 !important;
    color: #333 !important;
    border-color: #ccc !important;
    border-radius: 8px;
    padding: 8px 16px;
}

.xero-cancel-import:hover,
.xero-cancel-confirm:hover {
    background-color: #e2e2e2 !important;
    border-color: #999 !important;
}

/* Confirm/Start button styles */
.xero-confirm-sync {
    background-color: #2271b1 !important;
    color: white !important;
    border-color: #2271b1 !important;
    border-radius: 4px;
    font-weight: 600;
}

.xero-confirm-sync:hover {
    background-color: #135e96 !important;
    border-color: #135e96 !important;
}

/* Cancel import button (red) */
.xero-cancel-import {
    background-color: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
}

.xero-cancel-import:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
}

/* Processing state styles */
.processing .progress-bar-fill {
    transition: none;
    animation: progress-animation 2s linear infinite;
    background-image: linear-gradient(-45deg,
            rgba(255, 255, 255, 0.2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.2) 75%,
            transparent 75%,
            transparent);
    background-size: 50px 50px;
}

@keyframes progress-animation {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 50px 50px;
    }
}

/* Completed state styles */
.progress-bar-fill.complete {
    background-color: #28a745;
}

.xero-modal-header.import-complete {
    background-color: #28a745;
    color: white;
}

/* Sync Buttons Container */
.xero-sync-buttons {
    display: flex;
    gap: 10px;
}

/* Import Progress Styles */
.xero-import-progress {
    margin-bottom: 15px;
}

.progress-text {
    margin-top: 5px;
    font-size: 13px;
}

/* Rate Limit Alert Styles */
#rate-limit-info {
    margin-bottom: 15px;
    display: none;
}

.rate-limit-alert {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
}

.rate-limit-alert h4 {
    margin-top: 0;
    margin-bottom: 5px;
}

.rate-limit-message {
    margin-bottom: 5px;
}

.rate-limit-timer {
    font-weight: bold;
}
